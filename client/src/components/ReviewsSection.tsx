import { useScrollAnimation } from '@/hooks/useScrollAnimation';

const reviews = [
  {
    name: "<PERSON>",
    company: "TechStart Inc.",
    rating: 5,
    text: "Outstanding work! <PERSON><PERSON><PERSON><PERSON> delivered a complex ClickFunnels integration that exceeded our expectations. His attention to detail and communication throughout the project was exceptional.",
    project: "ClickFunnels Custom Integration"
  },
  {
    name: "<PERSON>",
    company: "Digital Marketing Pro",
    rating: 5,
    text: "Fantastic developer! Built our entire GoHighLevel automation system flawlessly. Fast delivery, clean code, and excellent support. Highly recommend!",
    project: "GoHighLevel Automation"
  },
  {
    name: "<PERSON>",
    company: "E-commerce Solutions",
    rating: 5,
    text: "<PERSON><PERSON><PERSON><PERSON> transformed our WordPress site into a high-converting sales machine. His expertise in both frontend and backend development is remarkable.",
    project: "WordPress E-commerce Site"
  },
  {
    name: "<PERSON>",
    company: "SaaS Startup",
    rating: 5,
    text: "Incredible React developer! Built our entire dashboard from scratch with beautiful animations and perfect functionality. Will definitely work with him again.",
    project: "React Dashboard Development"
  },
  {
    name: "<PERSON>",
    company: "Marketing Agency",
    rating: 5,
    text: "Professional, reliable, and talented. <PERSON><PERSON><PERSON><PERSON> integrated multiple APIs and created custom automations that saved us hours of manual work daily.",
    project: "API Integration & Automation"
  },
  {
    name: "<PERSON>",
    company: "Online Store",
    rating: 5,
    text: "Best Shopify developer on Fiverr! Created a stunning store with custom features that boosted our conversion rate by 40%. Amazing work!",
    project: "Custom Shopify Development"
  }
];

export default function ReviewsSection() {
  const { ref, isVisible } = useScrollAnimation();

  return (
    <section className="py-32 relative">
      <div className="max-w-6xl mx-auto px-8">
        <div 
          ref={ref}
          className={`transition-all duration-1000 ${
            isVisible ? 'animate-fade-in-up' : 'opacity-0 translate-y-12'
          }`}
        >
          <h2 className="font-bold text-4xl md:text-5xl text-center mb-16 glow-text font-mono">
            Client <span className="text-primary">Reviews</span>
          </h2>
          
          <div className="text-center mb-12">
            <div className="inline-flex items-center glassmorphism px-6 py-3 rounded-full">
              <div className="flex text-yellow-400 text-xl mr-3">
                {[...Array(5)].map((_, i) => (
                  <i key={i} className="fas fa-star"></i>
                ))}
              </div>
              <span className="text-lg font-semibold">4.9/5 Average Rating</span>
              <span className="text-muted-foreground ml-2">(500+ Reviews)</span>
            </div>
          </div>
          
          <div className="relative overflow-hidden">
            <div className="flex gap-4 animate-scroll" style={{ 
              width: `${reviews.length * 240}px`,
              animation: 'scroll 30s linear infinite'
            }}>
              {[...reviews, ...reviews].map((review, index) => (
                <div 
                  key={index}
                  className="glassmorphism p-4 rounded-xl flex-shrink-0"
                  style={{ minWidth: '220px' }}
                >
                  <div className="flex items-center mb-3">
                    <div className="w-8 h-8 bg-gradient-to-r from-primary to-[hsl(var(--space-accent))] rounded-full flex items-center justify-center mr-3">
                      <span className="text-white font-bold text-sm">
                        {review.name.charAt(0)}
                      </span>
                    </div>
                    <div>
                      <h4 className="font-semibold text-white text-sm">{review.name}</h4>
                      <p className="text-xs text-muted-foreground">{review.company}</p>
                    </div>
                  </div>
                  
                  <div className="flex text-yellow-400 mb-3">
                    {[...Array(review.rating)].map((_, i) => (
                      <i key={i} className="fas fa-star text-sm"></i>
                    ))}
                  </div>
                  
                  <p className="text-muted-foreground mb-4 leading-relaxed text-sm">
                    "{review.text}"
                  </p>
                  
                  <div className="border-t border-[hsl(var(--space-secondary))] pt-3">
                    <p className="text-xs text-primary font-semibold">
                      Project: {review.project}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          <div className="text-center mt-12">
            <a 
              href="https://www.fiverr.com/aincoder" 
              target="_blank" 
              rel="noopener noreferrer"
              className="inline-flex items-center bg-primary hover:bg-primary/80 px-8 py-4 rounded-full font-semibold transition-all duration-300"
            >
              <i className="fas fa-star mr-2"></i>
              View All Reviews on Fiverr
              <i className="fas fa-external-link-alt ml-2 text-sm"></i>
            </a>
          </div>
        </div>
      </div>
    </section>
  );
}