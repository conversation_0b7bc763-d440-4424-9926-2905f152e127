import { useEffect, useRef } from 'react';

interface Technology {
  name: string;
  color: string;
}

interface Node {
  x: number;
  y: number;
  vx: number;
  vy: number;
  tech: Technology;
  radius: number;
  targetRadius: number;
}

interface Connection {
  from: number;
  to: number;
  opacity: number;
  maxOpacity: number;
  growing: boolean;
}

interface TechnologyNetworkProps {
  technologies: Technology[];
}

export default function TechnologyNetwork({ technologies }: TechnologyNetworkProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const nodesRef = useRef<Node[]>([]);
  const connectionsRef = useRef<Connection[]>([]);
  const mouseRef = useRef({ x: 0, y: 0 });

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const resizeCanvas = () => {
      canvas.width = canvas.offsetWidth;
      canvas.height = canvas.offsetHeight;
    };

    const initializeNodes = () => {
      const nodes: Node[] = [];
      const centerX = canvas.width / 2;
      const centerY = canvas.height / 2;
      const minDistance = 80;

      technologies.forEach((tech, index) => {
        let x: number = 0, y: number = 0;
        let attempts = 0;
        
        do {
          const angle = (index / technologies.length) * 2 * Math.PI + (Math.random() - 0.5) * 0.8;
          const radius = 150 + Math.random() * 120;
          x = centerX + Math.cos(angle) * radius;
          y = centerY + Math.sin(angle) * radius * 0.7;
          attempts++;
        } while (
          attempts < 20 && 
          nodes.some(node => Math.sqrt((node.x - x) ** 2 + (node.y - y) ** 2) < minDistance)
        );

        nodes.push({
          x: Math.max(40, Math.min(canvas.width - 40, x)),
          y: Math.max(40, Math.min(canvas.height - 40, y)),
          vx: (Math.random() - 0.5) * 0.5,
          vy: (Math.random() - 0.5) * 0.5,
          tech,
          connections: []
        });
      });

      nodesRef.current = nodes;
      connectionsRef.current = [];
      setIsInitialized(true);
    };

    const createConnection = () => {
      const nodes = nodesRef.current;
      const connections = connectionsRef.current;
      
      if (nodes.length < 2) return;
      
      const from = Math.floor(Math.random() * nodes.length);
      let to = Math.floor(Math.random() * nodes.length);
      
      while (to === from) {
        to = Math.floor(Math.random() * nodes.length);
      }
      
      const distance = Math.sqrt(
        (nodes[from].x - nodes[to].x) ** 2 + (nodes[from].y - nodes[to].y) ** 2
      );
      
      if (distance < 200 && !connections.some(conn => 
        (conn.from === from && conn.to === to) || (conn.from === to && conn.to === from)
      )) {
        connections.push({
          from,
          to,
          opacity: 0,
          animating: true
        });
        
        nodes[from].connections.push(to);
        nodes[to].connections.push(from);
      }
    };

    const updateNodes = () => {
      const nodes = nodesRef.current;
      
      nodes.forEach(node => {
        node.x += node.vx;
        node.y += node.vy;
        
        if (node.x <= 30 || node.x >= canvas.width - 30) {
          node.vx *= -1;
          node.x = Math.max(30, Math.min(canvas.width - 30, node.x));
        }
        if (node.y <= 30 || node.y >= canvas.height - 30) {
          node.vy *= -1;
          node.y = Math.max(30, Math.min(canvas.height - 30, node.y));
        }
        
        node.vx *= 0.99;
        node.vy *= 0.99;
      });
    };

    const updateConnections = () => {
      const connections = connectionsRef.current;
      
      connections.forEach(connection => {
        if (connection.animating) {
          connection.opacity += 0.02;
          if (connection.opacity >= 0.6) {
            connection.animating = false;
          }
        } else {
          connection.opacity -= 0.005;
          if (connection.opacity <= 0) {
            const index = connections.indexOf(connection);
            connections.splice(index, 1);
            
            const nodes = nodesRef.current;
            const fromNode = nodes[connection.from];
            const toNode = nodes[connection.to];
            
            fromNode.connections = fromNode.connections.filter(c => c !== connection.to);
            toNode.connections = toNode.connections.filter(c => c !== connection.from);
          }
        }
      });
    };

    const render = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      const nodes = nodesRef.current;
      const connections = connectionsRef.current;
      
      // Draw connections
      connections.forEach(connection => {
        const fromNode = nodes[connection.from];
        const toNode = nodes[connection.to];
        
        if (fromNode && toNode) {
          const gradient = ctx.createLinearGradient(
            fromNode.x, fromNode.y, toNode.x, toNode.y
          );
          gradient.addColorStop(0, `${fromNode.tech.color}${Math.floor(connection.opacity * 255).toString(16).padStart(2, '0')}`);
          gradient.addColorStop(1, `${toNode.tech.color}${Math.floor(connection.opacity * 255).toString(16).padStart(2, '0')}`);
          
          ctx.strokeStyle = gradient;
          ctx.lineWidth = 2;
          ctx.beginPath();
          ctx.moveTo(fromNode.x, fromNode.y);
          ctx.lineTo(toNode.x, toNode.y);
          ctx.stroke();
        }
      });
      
      // Draw nodes
      nodes.forEach(node => {
        // Outer glow
        ctx.shadowColor = node.tech.color;
        ctx.shadowBlur = 15;
        
        // Main circle
        ctx.fillStyle = node.tech.color;
        ctx.beginPath();
        ctx.arc(node.x, node.y, 20, 0, Math.PI * 2);
        ctx.fill();
        
        // Inner circle
        ctx.shadowBlur = 0;
        ctx.fillStyle = '#ffffff';
        ctx.beginPath();
        ctx.arc(node.x, node.y, 8, 0, Math.PI * 2);
        ctx.fill();
        
        // Text
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 11px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(node.tech.name, node.x, node.y + 35);
      });
    };

    const animate = () => {
      updateNodes();
      updateConnections();
      render();
      
      // Randomly create new connections
      if (Math.random() < 0.03) {
        createConnection();
      }
      
      animationRef.current = requestAnimationFrame(animate);
    };

    resizeCanvas();
    initializeNodes();
    animate();

    window.addEventListener('resize', () => {
      resizeCanvas();
      if (isInitialized) {
        initializeNodes();
      }
    });

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      window.removeEventListener('resize', resizeCanvas);
    };
  }, [technologies, isInitialized]);

  return (
    <div className="relative min-h-[500px] overflow-hidden rounded-xl bg-gradient-to-br from-[hsl(var(--space-dark))] to-[hsl(var(--space-primary))]">
      <canvas
        ref={canvasRef}
        className="w-full h-full"
        style={{ minHeight: '500px' }}
      />
    </div>
  );
}