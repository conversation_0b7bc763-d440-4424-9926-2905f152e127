import { useScrollAnimation } from '@/hooks/useScrollAnimation';

export default function ThankYouSection() {
  const { ref, isVisible } = useScrollAnimation();

  return (
    <section ref={ref} className="py-20 relative overflow-hidden">{/* Transparent background */}
      
      {/* Floating particles */}
      <div className="absolute inset-0">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 bg-primary rounded-full opacity-20 animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${2 + Math.random() * 2}s`
            }}
          />
        ))}
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className={`text-center max-w-4xl mx-auto transition-all duration-1000 ${
          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
        }`}>
          
          {/* Title */}
          <h2 className="text-4xl md:text-5xl font-bold mb-8 bg-gradient-to-r from-primary via-accent to-primary bg-clip-text text-transparent font-mono">
            Thank You for Visiting!
          </h2>
          


          {/* Message */}
          <div className="space-y-6 text-lg text-white/80 leading-relaxed">
            <p className="font-medium">
              I hope you enjoyed exploring my portfolio and learning about my journey as a full-stack developer.
            </p>
            <p>
              Every project represents countless hours of dedication, learning, and pushing the boundaries of what's possible with code.
            </p>
            <p className="text-primary font-semibold">
              Let's build something amazing together! 🚀
            </p>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mt-12">
            <button 
              onClick={() => {
                const element = document.getElementById('contact');
                if (element) {
                  element.scrollIntoView({ behavior: 'smooth' });
                }
              }}
              className="px-8 py-4 bg-primary text-white rounded-full font-semibold hover:bg-primary/80 transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-primary/25"
            >
              Start a Project
            </button>
            <button 
              onClick={() => {
                const element = document.getElementById('about');
                if (element) {
                  element.scrollIntoView({ behavior: 'smooth' });
                }
              }}
              className="px-8 py-4 border-2 border-primary text-primary rounded-full font-semibold hover:bg-primary hover:text-white transition-all duration-300 hover:scale-105"
            >
              Learn More About Me
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}