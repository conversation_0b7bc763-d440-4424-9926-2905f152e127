import { useScrollAnimation } from '@/hooks/useScrollAnimation';

const skillCategories = [
  {
    title: "Frontend",
    icon: "fab fa-react",
    skills: [
      { name: "React & Next.js", level: 95 },
      { name: "Vue.js & Nuxt.js", level: 90 },
      { name: "TypeScript", level: 88 }
    ]
  },
  {
    title: "Backend",
    icon: "fas fa-server",
    skills: [
      { name: "Node.js & Express", level: 92 },
      { name: "Laravel & PHP", level: 85 },
      { name: "API Development", level: 90 }
    ]
  },
  {
    title: "Database & Cloud",
    icon: "fas fa-database",
    skills: [
      { name: "MySQL & MongoDB", level: 87 },
      { name: "Firebase & Supabase", level: 83 },
      { name: "Docker & DevOps", level: 80 }
    ]
  },
  {
    title: "Marketing Tech",
    icon: "fas fa-chart-line",
    skills: [
      { name: "HighLevel & ClickFunnels", level: 95 },
      { name: "Zapier & Make.com", level: 88 },
      { name: "AI Chatbots", level: 85 }
    ]
  },
  {
    title: "E-commerce",
    icon: "fas fa-shopping-cart",
    skills: [
      { name: "Shopify", level: 90 },
      { name: "WooCommerce", level: 87 },
      { name: "Stripe Integration", level: 92 }
    ]
  },
  {
    title: "CMS & Tools",
    icon: "fas fa-tools",
    skills: [
      { name: "WordPress", level: 93 },
      { name: "Git & Version Control", level: 89 },
      { name: "Project Management", level: 86 }
    ]
  }
];

const technologies = [
  { name: "React", color: "#61DAFB" },
  { name: "Vue.js", color: "#4FC08D" },
  { name: "Node.js", color: "#339933" },
  { name: "Laravel", color: "#FF2D20" },
  { name: "TypeScript", color: "#3178C6" },
  { name: "JavaScript", color: "#F7DF1E" },
  { name: "PHP", color: "#777BB4" },
  { name: "MySQL", color: "#4479A1" },
  { name: "MongoDB", color: "#47A248" },
  { name: "Docker", color: "#2496ED" },
  { name: "AWS", color: "#FF9900" },
  { name: "Firebase", color: "#FFCA28" },
  { name: "Supabase", color: "#3ECF8E" },
  { name: "Git", color: "#F05032" },
  { name: "WordPress", color: "#21759B" },
  { name: "Shopify", color: "#7AB55C" },
  { name: "Stripe", color: "#635BFF" },
  { name: "Zapier", color: "#FF4A00" },
  { name: "ClickFunnels", color: "#3366CC" },
  { name: "HighLevel", color: "#6C5CE7" }
];

function SkillBar({ skill }: { skill: { name: string; level: number } }) {
  return (
    <div className="skill-item">
      <div className="flex justify-between mb-2">
        <span className="text-sm">{skill.name}</span>
        <span className="text-sm text-primary">{skill.level}%</span>
      </div>
      <div className="w-full bg-gray-700 rounded-full h-3 skill-bar overflow-hidden">
        <div 
          className="skill-progress rounded-full"
          style={{ 
            width: `${skill.level}%`,
            background: 'linear-gradient(90deg, hsl(var(--primary)), hsl(var(--space-accent)))'
          }}
        ></div>
      </div>
    </div>
  );
}

export default function SkillsSection() {
  const { ref, isVisible } = useScrollAnimation();

  return (
    <section id="skills" className="py-32 relative">
      <div className="max-w-6xl mx-auto px-8">
        <div 
          ref={ref}
          className={`transition-all duration-1000 ${
            isVisible ? 'animate-fade-in-up' : 'opacity-0 translate-y-12'
          }`}
        >
          <h2 className="font-bold text-4xl md:text-5xl text-center mb-16 glow-text font-mono">
            Technical <span className="text-primary">Skills</span>
          </h2>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {skillCategories.map((category, index) => (
              <div 
                key={index}
                className="glassmorphism p-6 rounded-xl"
              >
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-primary to-[hsl(var(--space-accent))] rounded-full flex items-center justify-center mr-4">
                    <i className={`${category.icon} text-white text-xl`}></i>
                  </div>
                  <h3 className="font-bold text-xl text-primary font-mono">{category.title}</h3>
                </div>
                <div className="space-y-4">
                  {category.skills.map((skill, skillIndex) => (
                    <SkillBar 
                      key={skillIndex} 
                      skill={skill}
                    />
                  ))}
                </div>
              </div>
            ))}
          </div>
          
          {/* Technology Tags */}
          <div className="glassmorphism p-8 rounded-2xl">
            <h3 className="font-bold text-2xl text-center mb-8 text-primary font-mono">
              Technologies I Work With
            </h3>
            <div className="flex flex-wrap gap-3 justify-center">
              {technologies.map((tech) => (
                <span 
                  key={tech.name}
                  className="px-4 py-2 rounded-full text-white font-medium text-sm transition-all duration-300 hover:scale-105"
                  style={{ 
                    backgroundColor: tech.color,
                    boxShadow: `0 4px 15px ${tech.color}30`
                  }}
                >
                  {tech.name}
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
