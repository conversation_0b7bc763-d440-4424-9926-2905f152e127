export default function Footer() {
  return (
    <footer className="py-16 border-t border-[hsl(var(--space-secondary))]">
      <div className="max-w-6xl mx-auto px-8 text-center">
        <div className="glassmorphism p-8 rounded-2xl max-w-4xl mx-auto">
          <h3 className="font-bold text-2xl mb-4 glow-text font-mono">
            Al-<PERSON><PERSON> <span className="text-primary">Nerob</span>
          </h3>
          <p className="text-muted-foreground mb-6">
            Full Stack Developer passionate about creating exceptional digital experiences
          </p>
          <div className="flex justify-center space-x-4 mb-8">
            <a 
              href="mailto:<EMAIL>"
              className="hover-glow p-3 rounded-full glassmorphism transition-all duration-300"
              title="Email"
            >
              <i className="fas fa-envelope text-xl text-primary"></i>
            </a>
            <a 
              href="https://www.linkedin.com/in/ain477/" 
              target="_blank" 
              rel="noopener noreferrer"
              className="hover-glow p-3 rounded-full glassmorphism transition-all duration-300"
              title="LinkedIn"
            >
              <i className="fab fa-linkedin text-xl text-blue-500"></i>
            </a>
            <a 
              href="https://github.com/Ain477" 
              target="_blank" 
              rel="noopener noreferrer"
              className="hover-glow p-3 rounded-full glassmorphism transition-all duration-300"
              title="GitHub"
            >
              <i className="fab fa-github text-xl text-gray-400"></i>
            </a>
            <a 
              href="https://fb.com/nerob24" 
              target="_blank" 
              rel="noopener noreferrer"
              className="hover-glow p-3 rounded-full glassmorphism transition-all duration-300"
              title="Facebook"
            >
              <i className="fab fa-facebook text-xl text-blue-600"></i>
            </a>
            <a 
              href="https://wa.me/8801750690477" 
              target="_blank" 
              rel="noopener noreferrer"
              className="hover-glow p-3 rounded-full glassmorphism transition-all duration-300"
              title="WhatsApp"
            >
              <i className="fab fa-whatsapp text-xl text-green-500"></i>
            </a>
            <a 
              href="https://www.fiverr.com/aincoder" 
              target="_blank" 
              rel="noopener noreferrer"
              className="hover-glow p-3 rounded-full glassmorphism transition-all duration-300"
              title="Fiverr"
            >
              <i className="fas fa-briefcase text-xl text-primary"></i>
            </a>
          </div>
          <p className="text-sm text-muted-foreground">
            © 2025 Al-Amin Islam Nerob. Crafted with passion and modern web technologies.
          </p>
        </div>
      </div>
    </footer>
  );
}
