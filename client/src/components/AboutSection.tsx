import { useScrollAnimation } from '@/hooks/useScrollAnimation';

export default function AboutSection() {
  const { ref, isVisible } = useScrollAnimation();

  return (
    <section id="about" className="py-32 relative">
      <div className="max-w-6xl mx-auto px-8">
        <div 
          ref={ref}
          className={`transition-all duration-1000 ${
            isVisible ? 'animate-fade-in-up' : 'opacity-0 translate-y-12'
          }`}
        >
          <h2 className="font-bold text-4xl md:text-5xl text-center mb-16 glow-text font-mono">
            About <span className="text-primary">Me</span>
          </h2>
          
          <div className="max-w-4xl mx-auto">
            <div className="glassmorphism p-8 rounded-2xl">

              <p className="text-muted-foreground mb-6 leading-relaxed">
                Driven by a passion for building impactful web applications, I'm a versatile Full Stack Developer 
                with a strong track record of translating complex business requirements into high-performance, 
                scalable solutions. I thrive in dynamic environments, embrace challenges with enthusiasm, and 
                commit to continuous learning.
              </p>
              <p className="text-muted-foreground mb-6 leading-relaxed">
                My journey began in the PHP ecosystem, delivering high-quality Laravel and WordPress solutions, 
                which provided a deep understanding of web architecture. I have since evolved into modern 
                JavaScript frameworks, specializing in React, Vue.js, Next.js, Nuxt.js, and Node.js.
              </p>
              <p className="text-muted-foreground mb-6 leading-relaxed">
                Today, my core expertise lies in:
                <br />- Frontend: React, Vue.js, Next.js, Nuxt.js, TypeScript
                <br />- Backend: Node.js, Express, Laravel, PHP
                <br />- Databases: MySQL, PlanetScale, MongoDB, Firebase, Supabase
                <br />- Dev Tools: Git, Docker, Vercel, Netlify
              </p>
              
              <p className="text-muted-foreground mb-6 leading-relaxed">
                Beyond development, I specialize in marketing automation and AI chatbot development. My experience includes working directly with HighLevel (GoHighLevel) as a Full Stack Engineer, where I gained deep insights into marketing automation platforms and SaaS scalability. I've customized and extended platforms like ClickFunnels, HighLevel, and Kajabi, and architected complex automation workflows using Zapier, n8n, and Make.com.
              </p>
              
              <p className="text-muted-foreground mb-6 leading-relaxed">
                I also build intelligent AI-powered chatbots to enhance user experience and deliver robust e-commerce solutions on Shopify and WooCommerce, with seamless integration into tools like GetResponse and Stripe.
              </p>
              
              <p className="text-muted-foreground leading-relaxed">
                Known for my collaborative and resourceful approach, I'm a self-starter who thrives in remote, cross-functional teams. Let's connect if you're looking for a developer who combines strong technical skills with real-world business impact.
              </p>
              
              <div className="flex flex-wrap gap-4 mt-8">
                <div className="glassmorphism px-6 py-3 rounded-full hover-glow">
                  <i className="fas fa-code mr-2 text-primary"></i>15+ Years Experience
                </div>
                <div className="glassmorphism px-6 py-3 rounded-full hover-glow">
                  <i className="fas fa-star mr-2 text-primary"></i>4.9/5 Client Rating
                </div>
                <div className="glassmorphism px-6 py-3 rounded-full hover-glow">
                  <i className="fas fa-project-diagram mr-2 text-primary"></i>500+ Projects
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
