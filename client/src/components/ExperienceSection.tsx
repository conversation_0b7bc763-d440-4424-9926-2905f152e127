import { useScrollAnimation } from '@/hooks/useScrollAnimation';

const experiences = [
  {
    company: "Fiverr",
    position: "Freelance Web Developer",
    type: "Level 2 Seller",
    period: "2019 – Present",
    description: "Provide full-stack web development services, specializing in custom coding for ClickFunnels, GoHighLevel, WordPress, and custom PHP/Laravel applications. Consistently maintain 4.9-star average rating.",
    icon: "fas fa-star",
    side: "left"
  },
  {
    company: "HighLevel",
    position: "Full Stack Engineer",
    type: "Full-time",
    period: "July 2021 – September 2022",
    description: "Worked across the full stack using Node.js, TypeScript, ExpressJS, Vue.js, and Google Cloud to develop highly scalable solutions for the HighLevel marketing automation platform.",
    icon: "fas fa-layer-group",
    side: "right"
  },
  {
    company: "ClikView",
    position: "Senior Software Engineer",
    type: "Full-time",
    period: "October 2022 – December 2023",
    description: "Led development initiatives, contributing to the design, implementation, and maintenance of software applications.",
    icon: "fas fa-chart-line",
    side: "left"
  },
  {
    company: "autoPatient.co",
    position: "Software Engineer",
    type: "Full-time",
    period: "January 2024 – December 2024",
    description: "Developed and maintained sophisticated software solutions within the marketing and advertising technology space, focusing on healthcare automation systems.",
    icon: "fas fa-heartbeat",
    side: "right"
  },
  {
    company: "Super Star BD",
    position: "Software Engineer",
    type: "Remote",
    period: "2017 – 2019",
    description: "Designed, implemented, and managed front-end & back-end development using WordPress. Developed new features, ensured high performance/availability, and implemented responsive themes/plugins.",
    icon: "fas fa-code",
    side: "left"
  },
  {
    company: "FingerTech IT",
    position: "Team Leader (Web Development)",
    type: "Full-time",
    period: "2015 – 2017",
    description: "Led a web development team, managing projects involving WordPress, JavaScript, and Laravel. Provided technical leadership, coaching, and mentorship to ensure successful project delivery.",
    icon: "fas fa-users",
    side: "right"
  },
  {
    company: "wNext Generation IT",
    position: "Senior Web Developer",
    type: "Full-time",
    period: "2013 – 2014",
    description: "Managed the complete software development lifecycle from conception to deployment. Developed, designed, and documented projects using HTML, CSS, JavaScript, and Laravel.",
    icon: "fas fa-laptop-code",
    side: "left"
  },
  {
    company: "Upwork",
    position: "Pro Web Developer",
    type: "Independent Freelancer",
    period: "March 2010 – May 2016",
    description: "Managed full-stack development for numerous client projects using PHP (Laravel), JavaScript, HTML, and CSS. Developed user-friendly WordPress websites, achieving \"top-rated\" status for successful project delivery.",
    icon: "fas fa-globe",
    side: "right"
  }
];

export default function ExperienceSection() {
  const { ref, isVisible } = useScrollAnimation();

  return (
    <section id="experience" className="py-32 relative">
      <div className="max-w-6xl mx-auto px-8">
        <div 
          ref={ref}
          className={`transition-all duration-1000 ${
            isVisible ? 'animate-fade-in-up' : 'opacity-0 translate-y-12'
          }`}
        >
          <h2 className="font-bold text-4xl md:text-5xl text-center mb-16 glow-text font-mono">
            Professional <span className="text-primary">Experience</span>
          </h2>
          
          <div className="relative max-w-6xl mx-auto">
            {/* Timeline line */}
            <div className="absolute left-8 md:left-1/2 transform md:-translate-x-px h-full w-0.5 bg-gradient-to-b from-primary to-[hsl(var(--space-accent))] opacity-50"></div>
            
            {/* Experience Items */}
            <div className="space-y-16">
              {experiences.map((exp, index) => (
                <div 
                  key={index}
                  className={`relative flex items-center ${
                    exp.side === 'right' ? 'md:flex-row-reverse' : ''
                  }`}
                >
                  {/* Timeline dot */}
                  <div className="absolute left-6 md:left-1/2 transform md:-translate-x-1/2 w-4 h-4 bg-primary rounded-full shadow-lg animate-glow z-10"></div>
                  
                  {/* Content */}
                  <div className={`ml-16 md:ml-0 md:w-1/2 ${
                    exp.side === 'right' ? 'md:pl-8' : 'md:pr-8'
                  }`}>
                    <div className={`glassmorphism p-6 rounded-xl hover-glow transition-all duration-300 hover:scale-105 ${
                      exp.side === 'right' ? 'md:mr-0 md:ml-auto md:max-w-lg' : 'md:ml-0 md:mr-auto md:max-w-lg'
                    }`}>
                      <div className="flex items-center mb-4">
                        <div className="w-12 h-12 bg-gradient-to-r from-primary to-[hsl(var(--space-accent))] rounded-full flex items-center justify-center mr-4">
                          <i className={`${exp.icon} text-white text-xl`}></i>
                        </div>
                        <div>
                          <h3 className="font-bold text-xl text-primary font-mono">{exp.company}</h3>
                          <p className="text-muted-foreground">{exp.position}</p>
                        </div>
                      </div>
                      <p className="text-sm text-primary mb-4 font-semibold">{exp.period}</p>
                      <p className="text-muted-foreground leading-relaxed">{exp.description}</p>
                      <div className="mt-4">
                        <span className="inline-block bg-primary/20 text-primary px-3 py-1 rounded-full text-xs font-semibold">
                          {exp.type}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  {/* Empty space for timeline balance */}
                  <div className="hidden md:block md:w-1/2"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
