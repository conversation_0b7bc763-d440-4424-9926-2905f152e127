import { useState } from 'react';
import { useScrollAnimation } from '@/hooks/useScrollAnimation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { insertContactMessageSchema, type InsertContactMessage } from '@shared/schema';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';

export default function ContactSection() {
  const { ref, isVisible } = useScrollAnimation();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<InsertContactMessage>({
    resolver: zodResolver(insertContactMessageSchema),
    defaultValues: {
      name: '',
      email: '',
      message: '',
      projectType: ''
    }
  });

  const onSubmit = async (data: InsertContactMessage) => {
    setIsSubmitting(true);
    try {
      const response = await apiRequest('POST', '/api/contact', data);
      const result = await response.json();
      
      if (result.success) {
        toast({
          title: "Message sent successfully!",
          description: "Thank you for reaching out. I'll get back to you soon.",
        });
        form.reset();
      } else {
        throw new Error(result.message || 'Failed to send message');
      }
    } catch (error) {
      console.error('Contact form error:', error);
      toast({
        title: "Failed to send message",
        description: "Please try again or contact me directly via email.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section id="contact" className="py-32 relative">
      <div className="max-w-6xl mx-auto px-8">
        <div 
          ref={ref}
          className={`transition-all duration-1000 ${
            isVisible ? 'animate-fade-in-up' : 'opacity-0 translate-y-12'
          }`}
        >
          <h2 className="font-bold text-4xl md:text-5xl text-center mb-16 glow-text font-mono">
            Let's <span className="text-primary">Connect</span>
          </h2>
          
          <div className="grid md:grid-cols-2 gap-16 items-start">
            {/* Contact Info */}
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <a 
                  href="mailto:<EMAIL>"
                  className="glassmorphism p-4 rounded-xl hover-glow transition-all duration-300 hover:scale-105 text-center"
                >
                  <i className="fas fa-envelope text-3xl text-primary mb-2"></i>
                  <p className="text-sm font-semibold">Email</p>
                </a>
                
                <a 
                  href="https://www.linkedin.com/in/ain477/" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="glassmorphism p-4 rounded-xl hover-glow transition-all duration-300 hover:scale-105 text-center"
                >
                  <i className="fab fa-linkedin text-3xl text-blue-500 mb-2"></i>
                  <p className="text-sm font-semibold">LinkedIn</p>
                </a>
                
                <a 
                  href="https://wa.me/8801750690477" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="glassmorphism p-4 rounded-xl hover-glow transition-all duration-300 hover:scale-105 text-center"
                >
                  <i className="fab fa-whatsapp text-3xl text-green-500 mb-2"></i>
                  <p className="text-sm font-semibold">WhatsApp</p>
                </a>
                
                <a 
                  href="https://fb.com/nerob24" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="glassmorphism p-4 rounded-xl hover-glow transition-all duration-300 hover:scale-105 text-center"
                >
                  <i className="fab fa-facebook text-3xl text-blue-600 mb-2"></i>
                  <p className="text-sm font-semibold">Facebook</p>
                </a>
                
                <a 
                  href="https://github.com/Ain477" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="glassmorphism p-4 rounded-xl hover-glow transition-all duration-300 hover:scale-105 text-center"
                >
                  <i className="fab fa-github text-3xl text-gray-400 mb-2"></i>
                  <p className="text-sm font-semibold">GitHub</p>
                </a>
                
                <a 
                  href="https://www.fiverr.com/aincoder" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="glassmorphism p-4 rounded-xl hover-glow transition-all duration-300 hover:scale-105 text-center"
                >
                  <i className="fas fa-briefcase text-3xl text-primary mb-2"></i>
                  <p className="text-sm font-semibold">Fiverr</p>
                </a>
              </div>
              
              {/* Availability Status */}
              <div className="glassmorphism p-6 rounded-2xl text-center">
                <div className="flex items-center justify-center">
                  <div className="w-4 h-4 bg-green-400 rounded-full mr-3 animate-pulse"></div>
                  <span className="text-green-400 font-semibold">Available for new projects</span>
                </div>
              </div>
            </div>
            
            {/* Contact Form */}
            <div className="glassmorphism p-8 rounded-2xl hover-glow">
              <h3 className="font-bold text-2xl mb-6 text-center text-primary font-mono">Get In Touch</h3>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <div>
                  <input 
                    type="text" 
                    placeholder="Your Name"
                    {...form.register('name')}
                    className="w-full bg-[hsl(var(--space-primary))] border border-[hsl(var(--space-secondary))] rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:border-primary focus:outline-none transition-colors"
                  />
                  {form.formState.errors.name && (
                    <p className="text-red-400 text-sm mt-1">{form.formState.errors.name.message}</p>
                  )}
                </div>
                
                <div>
                  <input 
                    type="email" 
                    placeholder="Your Email"
                    {...form.register('email')}
                    className="w-full bg-[hsl(var(--space-primary))] border border-[hsl(var(--space-secondary))] rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:border-primary focus:outline-none transition-colors"
                  />
                  {form.formState.errors.email && (
                    <p className="text-red-400 text-sm mt-1">{form.formState.errors.email.message}</p>
                  )}
                </div>
                
                <div>
                  <select 
                    {...form.register('projectType')}
                    className="w-full bg-[hsl(var(--space-primary))] border border-[hsl(var(--space-secondary))] rounded-lg px-4 py-3 text-white focus:border-primary focus:outline-none transition-colors"
                  >
                    <option value="">Select Project Type</option>
                    <option value="Web Application">Web Application</option>
                    <option value="E-commerce Site">E-commerce Site</option>
                    <option value="API Development">API Development</option>
                    <option value="Consultation">Consultation</option>
                    <option value="Other">Other</option>
                  </select>
                </div>
                
                <div>
                  <textarea 
                    rows={4} 
                    placeholder="Tell me about your project..."
                    {...form.register('message')}
                    className="w-full bg-[hsl(var(--space-primary))] border border-[hsl(var(--space-secondary))] rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:border-primary focus:outline-none transition-colors resize-none"
                  ></textarea>
                  {form.formState.errors.message && (
                    <p className="text-red-400 text-sm mt-1">{form.formState.errors.message.message}</p>
                  )}
                </div>
                
                <button 
                  type="submit" 
                  disabled={isSubmitting}
                  className="w-full bg-primary hover:bg-primary/80 text-white font-semibold py-4 rounded-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? (
                    <>
                      <i className="fas fa-spinner fa-spin mr-2"></i>Sending...
                    </>
                  ) : (
                    <>
                      <i className="fas fa-paper-plane mr-2"></i>Send Message
                    </>
                  )}
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
