# Al-Amin <PERSON>b

*   [<EMAIL>](mailto:<EMAIL>)
*   [LinkedIn Profile](https://www.linkedin.com/in/ain477/)
*   [Fiverr Profile](https://www.fiverr.com/aincoder)

## Summary

Driven by a passion for building impactful web applications, I’m a versatile Full Stack Developer with a strong track record of translating complex business requirements into high-performance, scalable solutions. I thrive in dynamic environments, embrace challenges with enthusiasm, and commit to continuous learning. My journey began in the PHP ecosystem, delivering high-quality Laravel and WordPress solutions, which provided a deep understanding of web architecture. I have since transitioned into the modern JavaScript stack, specializing in both front-end and back-end development, alongside marketing automation and AI chatbot implementation. Known for my collaborative and resourceful approach, I excel in remote, cross-functional teams and am dedicated to combining strong technical skills with real-world business impact.

## Core Competencies & Expertise

*   **Frontend Development:** React, Vue.js, Next.js, Nuxt.js, TypeScript, JavaScript, HTML5, CSS, Bootstrap
*   **Backend Development:** Node.js, Express, Laravel, PHP
*   **Databases:** MySQL, PlanetScale, MongoDB, Firebase (Firestore & Realtime DB), Supabase
*   **DevOps & Tools:** Git, Docker, Vercel, Netlify
*   **Marketing Automation & CRM:** HighLevel (GoHighLevel), ClickFunnels, Kajabi, Zapier, n8n, Make.com
*   **AI & Chatbots:** Development and integration of AI-powered chatbots
*   **E-commerce:** Shopify, WooCommerce, Stripe Integration
*   **API Development & Integration:** RESTful APIs, Third-party API integration
*   **CMS & Funnel Builders:** WordPress, Leadpages, ClickFunnels
*   **Other Skills:** Software Development Life Cycle (SDLC), Project Management, Technical Troubleshooting, Communication


## Professional Experience

**LogRocket** - *Content Advisory Board Member* (Freelance)
*February 2025 – Present*

*   Contribute expertise to shape content strategy and ensure technical accuracy for a leading developer resource platform.

**autoPatient.co** - *Software Engineer* (Full-time)
*January 2024 – December 2024*

*   Developed and maintained software solutions within the marketing and advertising technology space.

**ClikView** - *Senior Software Engineer* (Full-time)
*October 2022 – December 2023*

*   Led development initiatives, contributing to the design, implementation, and maintenance of software applications.

**HighLevel** - *Full Stack Engineer* (Full-time)
*July 2021 – September 2022*

*   Worked across the full stack (Node.js, TypeScript, ExpressJS, Vue.js, Google Cloud) to develop highly scalable solutions for the HighLevel marketing automation platform.
*   Gained deep insights into SaaS scalability and marketing automation workflows.
*   Maintained application performance, uptime, scalability, and high code quality standards.

**Fiverr** - *Freelance Web Developer* (Level 2 Seller)
*2019 – Present*

*   Provide full-stack web development services, specializing in custom coding for ClickFunnels, GoHighLevel, WordPress, and custom PHP/Laravel applications.
*   Offer API development, integration services, and funnel design (landing pages).
*   Consistently receive positive client reviews (4.9-star average rating) for delivering high-quality projects on time.
*   Expertise in ClickFunnels, Leadpages, WordPress, Sales Funnels, and general Web Development.

**Upwork** - *Pro Web Developer* (Independent Freelancer)
*March 2010 – May 2016* (Note: Dates differ slightly from original resume, using LinkedIn data as more recent)

*   Managed full-stack development for numerous client projects using PHP (Laravel), JavaScript, HTML, and CSS.
*   Developed user-friendly WordPress websites, achieving "top-rated" status for successful project delivery and client satisfaction.

**Super Star BD** - *Software Engineer* (Remote)
*2017 – 2019*

*   Designed, implemented, and managed front-end & back-end development using WordPress.
*   Developed new features, ensured high performance/availability, and implemented responsive themes/plugins.

**FingerTech IT** - *Team Leader (Web Development)*
*2015 – 2017*

*   Led a web development team, managing projects involving WordPress, JavaScript, and Laravel.
*   Provided technical leadership, coaching, and mentorship to ensure successful project delivery.
*   Analyzed and resolved technical problems, adhering to high-quality development principles.

**wNext Generation IT** - *Senior Web Developer*
*2013 – 2014*

*   Managed the complete software development lifecycle from conception to deployment.
*   Developed, designed, and documented projects using HTML, CSS, JavaScript, and Laravel.
*   Modified and tested changes to previously developed programs.

## Education

**Mohim Institution, Faridpur, Dhaka**
*Secondary School Certificate (SSC)*
*January 2009 – December 2011*

**(Note:** Original resume listed Higher Secondary School Certificate from Gov. Yasin College. LinkedIn data only shows SSC from Mohim Institution. Retained SSC as per LinkedIn.)

