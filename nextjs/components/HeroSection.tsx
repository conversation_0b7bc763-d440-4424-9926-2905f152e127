'use client';

import { useScrollAnimation } from '@/hooks/useScrollAnimation';
import { useEffect } from 'react';

export default function HeroSection() {
  const { ref, isVisible } = useScrollAnimation();

  useEffect(() => {
    // Initialize particles.js
    if (typeof window !== 'undefined' && (window as any).particlesJS) {
      (window as any).particlesJS('particles-js', {
        particles: {
          number: {
            value: 160,
            density: {
              enable: true,
              value_area: 800
            }
          },
          color: {
            value: "#E94560"
          },
          shape: {
            type: "circle",
            stroke: {
              width: 0,
              color: "#000000"
            },
            polygon: {
              nb_sides: 5
            }
          },
          opacity: {
            value: 0.5,
            random: false,
            anim: {
              enable: false,
              speed: 1,
              opacity_min: 0.1,
              sync: false
            }
          },
          size: {
            value: 3,
            random: true,
            anim: {
              enable: false,
              speed: 40,
              size_min: 0.1,
              sync: false
            }
          },
          line_linked: {
            enable: true,
            distance: 150,
            color: "#E94560",
            opacity: 0.4,
            width: 1
          },
          move: {
            enable: true,
            speed: 6,
            direction: "none",
            random: false,
            straight: false,
            out_mode: "out",
            bounce: false,
            attract: {
              enable: false,
              rotateX: 600,
              rotateY: 1200
            }
          }
        },
        interactivity: {
          detect_on: "canvas",
          events: {
            onhover: {
              enable: true,
              mode: "repulse"
            },
            onclick: {
              enable: true,
              mode: "push"
            },
            resize: true
          },
          modes: {
            grab: {
              distance: 400,
              line_linked: {
                opacity: 1
              }
            },
            bubble: {
              distance: 400,
              size: 40,
              duration: 2,
              opacity: 8,
              speed: 3
            },
            repulse: {
              distance: 200,
              duration: 0.4
            },
            push: {
              particles_nb: 4
            },
            remove: {
              particles_nb: 2
            }
          }
        },
        retina_detect: true
      });
    }
  }, []);

  const scrollToAbout = () => {
    const element = document.getElementById('about');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="home" className="min-h-screen flex items-center justify-center relative overflow-hidden">
      {/* Particles.js Background */}
      <div id="particles-js" className="absolute inset-0 z-0"></div>
      
      {/* Space Nebula Background */}
      <div className="absolute inset-0 z-0">
        {/* Primary nebula gradient */}
        <div className="absolute inset-0 bg-gradient-radial from-purple-900/30 via-blue-900/20 to-transparent"></div>
        <div className="absolute inset-0 bg-gradient-to-br from-[hsl(var(--space-dark))] via-[hsl(var(--space-primary))] to-[hsl(var(--space-secondary))]"></div>
        
        {/* Nebula clouds */}
        <div className="absolute top-0 left-0 w-full h-full">
          <div className="absolute top-1/4 left-1/3 w-96 h-96 bg-gradient-radial from-purple-500/10 via-pink-500/5 to-transparent rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/3 right-1/4 w-80 h-80 bg-gradient-radial from-blue-500/10 via-cyan-500/5 to-transparent rounded-full blur-3xl"></div>
          <div className="absolute top-1/2 left-1/6 w-64 h-64 bg-gradient-radial from-primary/15 via-orange-500/8 to-transparent rounded-full blur-2xl"></div>
        </div>
        
        {/* Galaxy particles */}
        <div className="absolute inset-0">
          <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            {/* Galaxy spiral arms */}
            <path
              d="M20,50 Q30,30 50,35 Q70,40 80,20"
              stroke="url(#galaxyGradient1)"
              strokeWidth="0.2"
              fill="none"
              opacity="0.6"
            />
            <path
              d="M80,50 Q70,70 50,65 Q30,60 20,80"
              stroke="url(#galaxyGradient2)"
              strokeWidth="0.2"
              fill="none"
              opacity="0.6"
            />
            
            <defs>
              <linearGradient id="galaxyGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#E94560" stopOpacity="0.8" />
                <stop offset="50%" stopColor="#A855F7" stopOpacity="0.4" />
                <stop offset="100%" stopColor="#3B82F6" stopOpacity="0.2" />
              </linearGradient>
              <linearGradient id="galaxyGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#3B82F6" stopOpacity="0.8" />
                <stop offset="50%" stopColor="#06B6D4" stopOpacity="0.4" />
                <stop offset="100%" stopColor="#E94560" stopOpacity="0.2" />
              </linearGradient>
            </defs>
            
            {/* Particle connections */}
            {Array.from({ length: 15 }, (_, i) => {
              const x1 = 10 + (i * 6) % 80;
              const y1 = 20 + (i * 4) % 60;
              const x2 = 15 + ((i + 5) * 6) % 80;
              const y2 = 25 + ((i + 3) * 4) % 60;
              
              return (
                <g key={i}>
                  <circle cx={x1} cy={y1} r="0.1" fill="#E94560" opacity="0.8" />
                  <circle cx={x2} cy={y2} r="0.1" fill="#3B82F6" opacity="0.6" />
                  <line 
                    x1={x1} y1={y1} x2={x2} y2={y2} 
                    stroke="url(#galaxyGradient1)" 
                    strokeWidth="0.05" 
                    opacity="0.3"
                  />
                </g>
              );
            })}
          </svg>
        </div>
        
        {/* Floating cosmic elements - static */}
        <div className="absolute top-20 left-20 w-32 h-32 bg-gradient-to-r from-primary/20 to-[hsl(var(--space-accent))]/20 rounded-full"></div>
        <div className="absolute bottom-32 right-32 w-24 h-24 bg-gradient-to-r from-[hsl(var(--space-accent))]/20 to-primary/20 rounded-full"></div>
        <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-gradient-to-r from-primary/20 to-[hsl(var(--space-secondary))]/20 rounded-full"></div>
      </div>
      
      <div 
        ref={ref}
        className={`max-w-6xl mx-auto px-8 text-center z-10 relative transition-all duration-1000 ${
          isVisible ? 'animate-fade-in-up' : 'opacity-0 translate-y-12'
        }`}
      >
        <h1 className="font-bold text-5xl md:text-7xl mb-6 glow-text font-mono">
          Al-Amin Islam <span className="text-primary">Nerob</span>
        </h1>
        <h2 className="text-xl md:text-2xl mb-8 text-muted-foreground">
          Full-Stack Developer | Vue, React, Next.js, Laravel | TypeScript, Node.js, PHP | AI & Chatbots | Marketing Automation (Zapier, n8n, HighLevel)
        </h2>
        <p className="text-lg mb-12 max-w-3xl mx-auto leading-relaxed text-muted-foreground">
          Passionate about building impactful web applications with modern technologies. 
          Specialized in React, Node.js, and scalable cloud solutions.
        </p>
        
        <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
          <button 
            onClick={scrollToAbout}
            className="bg-primary hover:bg-primary/80 px-8 py-4 rounded-full font-semibold transition-all duration-300"
          >
            <i className="fas fa-envelope mr-2"></i>Get In Touch
          </button>
          
          <div className="flex space-x-6">
            <a 
              href="https://www.linkedin.com/in/ain477/" 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-3xl hover:text-primary transition-colors duration-300"
              title="LinkedIn"
            >
              <i className="fab fa-linkedin"></i>
            </a>
            <a 
              href="mailto:<EMAIL>"
              className="text-3xl hover:text-primary transition-colors duration-300"
              title="Email"
            >
              <i className="fas fa-envelope"></i>
            </a>
            <a 
              href="https://github.com/Ain477" 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-3xl hover:text-primary transition-colors duration-300"
              title="GitHub"
            >
              <i className="fab fa-github"></i>
            </a>
            <a 
              href="https://fb.com/nerob24" 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-3xl hover:text-primary transition-colors duration-300"
              title="Facebook"
            >
              <i className="fab fa-facebook"></i>
            </a>
            <a 
              href="https://wa.me/8801750690477" 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-3xl hover:text-primary transition-colors duration-300"
              title="WhatsApp"
            >
              <i className="fab fa-whatsapp"></i>
            </a>
            <a 
              href="https://www.fiverr.com/aincoder" 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-3xl hover:text-primary transition-colors duration-300"
              title="Fiverr"
            >
              <i className="fas fa-briefcase"></i>
            </a>
          </div>
        </div>
      </div>
      
      {/* Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <i className="fas fa-chevron-down text-2xl text-primary"></i>
      </div>
    </section>
  );
}
