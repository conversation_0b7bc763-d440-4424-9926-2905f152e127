@import "tailwindcss";

:root {
  --background: 210 11% 6%; /* #0A0A1B */
  --foreground: 0 0% 98%;
  --muted: 240 3.7% 15.9%;
  --muted-foreground: 240 5% 64.9%;
  --popover: 240 10% 3.9%;
  --popover-foreground: 0 0% 98%;
  --card: 240 10% 3.9%;
  --card-foreground: 0 0% 98%;
  --border: 240 3.7% 15.9%;
  --input: 240 3.7% 15.9%;
  --primary: 345 83% 47%; /* #E94560 */
  --primary-foreground: 211 100% 99%;
  --secondary: 240 3.7% 15.9%;
  --secondary-foreground: 0 0% 98%;
  --accent: 240 3.7% 15.9%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --ring: 240 4.9% 83.9%;
  --radius: 0.5rem;

  /* Custom space theme colors */
  --space-dark: 236 44% 5%; /* #0A0A1B */
  --space-primary: 236 43% 14%; /* #1A1A2E */
  --space-secondary: 221 42% 19%; /* #16213E */
  --space-accent: 207 63% 22%; /* #0F3460 */
  --neon-accent: 345 83% 47%; /* #E94560 */
  --text-muted: 225 14% 70%; /* #B8BCC8 */
}

@theme inline {
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));
  --color-primary: hsl(var(--primary));
  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

.dark {
  --background: 236 44% 5%; /* #0A0A1B */
  --foreground: 0 0% 98%;
  --muted: 240 3.7% 15.9%;
  --muted-foreground: 240 5% 64.9%;
  --popover: 240 10% 3.9%;
  --popover-foreground: 0 0% 98%;
  --card: 240 10% 3.9%;
  --card-foreground: 0 0% 98%;
  --border: 240 3.7% 15.9%;
  --input: 240 3.7% 15.9%;
  --primary: 345 83% 47%; /* #E94560 */
  --primary-foreground: 211 100% 99%;
  --secondary: 240 3.7% 15.9%;
  --secondary-foreground: 0 0% 98%;
  --accent: 240 3.7% 15.9%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --ring: 240 4.9% 83.9%;
  --radius: 0.5rem;
}

* {
  border-color: hsl(var(--border));
}

body {
  background: linear-gradient(135deg, hsl(var(--space-dark)) 0%, hsl(var(--space-primary)) 100%);
  color: hsl(var(--foreground));
  font-family: var(--font-geist-sans), Arial, Helvetica, sans-serif;
  min-height: 100vh;
  font-feature-settings: "rlig" 1, "calt" 1;
}

html {
  scroll-behavior: smooth;
}

.glassmorphism {
  background: rgba(26, 26, 46, 0.25);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.glow-text {
  text-shadow: 0 0 20px hsl(var(--neon-accent) / 0.5);
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

.bg-gradient-radial {
  background: radial-gradient(circle, var(--tw-gradient-stops));
}

@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

@keyframes expandLine {
  0%, 100% {
    transform: scaleX(0);
    opacity: 0;
  }
  50% {
    transform: scaleX(1);
    opacity: 1;
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

.animate-slide-in-left {
  animation: slideInLeft 0.8s ease-out forwards;
}

.animate-slide-in-right {
  animation: slideInRight 0.8s ease-out forwards;
}

.animate-on-scroll {
  opacity: 0;
  transform: translateY(60px);
  transition: all 0.8s ease-out;
}

.animate-on-scroll.visible {
  opacity: 1;
  transform: translateY(0);
}

.skill-bar {
  position: relative;
  overflow: hidden;
}

.skill-progress {
  height: 100%;
  background: linear-gradient(90deg, hsl(var(--neon-accent)), hsl(var(--space-accent)));
  transition: width 2s ease-out;
  box-shadow: 0 0 10px hsl(var(--neon-accent) / 0.5);
  transform-origin: left;
  transform: scaleX(0);
}

.skill-progress.animate {
  transform: scaleX(1);
}

.hover-glow {
  transition: all 0.3s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 30px hsl(var(--neon-accent) / 0.4);
  transform: translateY(-2px);
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes glow {
  0% {
    box-shadow: 0 0 20px hsl(var(--neon-accent) / 0.5);
  }
  100% {
    box-shadow: 0 0 40px hsl(var(--neon-accent) / 0.8);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(60px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  0% {
    opacity: 0;
    transform: translateX(-60px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  0% {
    opacity: 0;
    transform: translateX(60px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes particle-float {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100vh) rotate(360deg);
    opacity: 0;
  }
}

.particle {
  position: fixed;
  width: 4px;
  height: 4px;
  background: hsl(var(--neon-accent));
  border-radius: 50%;
  pointer-events: none;
  z-index: 1;
  animation: particle-float 20s linear infinite;
}

@media (prefers-reduced-motion: reduce) {
  .animate-float,
  .animate-glow,
  .particle,
  .animate-on-scroll {
    animation: none !important;
    transition: none !important;
  }

  html {
    scroll-behavior: auto;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--space-primary));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--neon-accent));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--neon-accent) / 0.8);
}
