import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import { QueryClientProvider } from "@tanstack/react-query";
import { queryClient } from "@/lib/queryClient";
import { Toaster } from "@/components/ui/toaster";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Al-Amin Islam Nerob - Full Stack Developer",
  description: "Full-Stack Developer | Vue, React, Next.js, Laravel | TypeScript, Node.js, PHP | AI & Chatbots | Marketing Automation (Zapier, n8n, HighLevel)",
  keywords: "Full Stack Developer, React, Next.js, Vue.js, <PERSON><PERSON>, <PERSON><PERSON>, Node.js, <PERSON><PERSON>, AI Chatbots, Marketing Automation",
  authors: [{ name: "Al-Amin <PERSON>b" }],
  openGraph: {
    title: "Al-Amin <PERSON>b - Full Stack Developer",
    description: "Full-Stack Developer specializing in React, Vue.js, Next.js, Laravel, and marketing automation",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <head>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
        <script src="https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js" async></script>
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <QueryClientProvider client={queryClient}>
          <Toaster />
          {children}
        </QueryClientProvider>
      </body>
    </html>
  );
}
